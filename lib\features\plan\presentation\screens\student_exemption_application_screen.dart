/// -----
/// student_exemption_application_screen.dart
/// 
/// 学生免实习申请页面，允许学生提交免实习申请及上传相关证明文件
///
/// <AUTHOR>
/// @date 2025-05-27
/// @copyright Copyright © 2025 亿硕教育
/// -----

// Dart 标准库
import 'dart:io';

// Flutter 框架库
import 'package:flutter/material.dart';

// 第三方库
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import 'package:image_picker/image_picker.dart';

// 项目内部库
import 'package:flutter_demo/core/storage/local_storage.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/course_header_section.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/features/plan/presentation/bloc/exemption_application_bloc.dart';
import 'package:flutter_demo/features/plan/presentation/bloc/exemption_application_event.dart';
import 'package:flutter_demo/features/plan/presentation/bloc/exemption_application_state.dart';
import 'package:flutter_demo/features/upload/domain/usecases/upload_file_usecase.dart';

/// 学生免实习申请页面
///
/// 学生可以在此页面选择实习学期、填写免实习原因、上传证明文件并提交申请
/// 支持上传1个证明文件
class StudentExemptionApplicationScreen extends StatefulWidget {
  const StudentExemptionApplicationScreen({Key? key}) : super(key: key);

  @override
  State<StudentExemptionApplicationScreen> createState() => _StudentExemptionApplicationScreenState();
}

class _StudentExemptionApplicationScreenState extends State<StudentExemptionApplicationScreen> {
  // BLoC实例
  late ExemptionApplicationBloc _exemptionApplicationBloc;

  // 本地存储
  final LocalStorage _localStorage = GetIt.instance<LocalStorage>();

  // 学生去向，默认为"参军"
  final String _studentDirection = '参军';

  // 证明文件
  File? _proofDocument;

  // 表单控制器
  final TextEditingController _reasonController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // 初始化BLoC
    _exemptionApplicationBloc = GetIt.instance<ExemptionApplicationBloc>();

    // 设置默认免实习理由
    _reasonController.text = _studentDirection;
  }

  @override
  void dispose() {
    _reasonController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _exemptionApplicationBloc,
      child: BlocListener<ExemptionApplicationBloc, ExemptionApplicationState>(
        listener: (context, state) {
          if (state is ExemptionApplicationSubmittedState) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(state.message)),
            );
            // 提交成功后返回上一页，并通知需要刷新
            Future.delayed(const Duration(seconds: 1), () {
              if (mounted) {
                Navigator.pop(context, true); // 返回true表示需要刷新
              }
            });
          } else if (state is ExemptionApplicationFailureState) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        child: Scaffold(
          backgroundColor: AppTheme.backgroundColor,
          appBar: const CustomAppBar(
            title: '免实习申请',
            backgroundColor: Colors.white,
          ),
          body: Column(
            children: [
              // 内容区域（可滚动）
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 学期选择 - 使用动态数据
                      const CourseHeaderSection(),

                      SizedBox(height: 21.h),

                      // 免实习申请信息
                      _buildApplicationInfo(),

                      SizedBox(height: 10.h),

                      // 证明文件上传
                      _buildDocumentUpload(),

                      SizedBox(height: 30.h),
                    ],
                  ),
                ),
              ),

              // 提交按钮（固定在底部）
              _buildSubmitButton(),
            ],
          ),
        ),
      ),
    );
  }



  // 免实习申请信息
  Widget _buildApplicationInfo() {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '免实习申请信息',
            style: TextStyle(
              fontSize: 30.sp,
              fontWeight: FontWeight.bold,
              color: AppTheme.black333,
            ),
          ),
          SizedBox(height: 28.h),

          // 免实习理由输入框
          Text(
            '免实习理由:',
            style: TextStyle(
              fontSize: 28.sp,
              color: AppTheme.black333,
            ),
          ),
          SizedBox(height: 12.h),
          TextField(
            controller: _reasonController,
            maxLines: 3,
            decoration: InputDecoration(
              hintText: '请输入免实习理由',
              hintStyle: TextStyle(
                color: Colors.grey[400],
                fontSize: 14.sp,
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
                borderSide: const BorderSide(color: AppTheme.blue2165f6),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
                borderSide: const BorderSide(color: AppTheme.blue2165f6),
              ),
              contentPadding: EdgeInsets.all(12.w),
            ),
          ),
        ],
      ),
    );
  }

  // 证明文件上传区域
  Widget _buildDocumentUpload() {
    return Container(
      width: double.infinity,
      color: Colors.white,
      padding: EdgeInsets.symmetric(horizontal: 20, vertical: 40.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '证明文件上传',
            style: TextStyle(
              fontSize: 30.sp,
              fontWeight: FontWeight.bold,
              color: AppTheme.black333,
            ),
          ),
          const SizedBox(height: 16),
          _proofDocument == null
              ? _buildAddDocumentButton()
              : _buildDocumentPreview(),
        ],
      ),
    );
  }

  // 添加文件按钮
  Widget _buildAddDocumentButton() {
    return GestureDetector(
      onTap: _pickDocument,
      child: Container(
        width: 100,
        height: 100,
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Center(
          child: Icon(
            Icons.add,
            size: 40,
            color: Colors.grey,
          ),
        ),
      ),
    );
  }

  // 文件预览
  Widget _buildDocumentPreview() {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        Container(
          width: 100,
          height: 100,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
            image: DecorationImage(
              image: FileImage(_proofDocument!),
              fit: BoxFit.cover,
            ),
          ),
        ),
        Positioned(
          top: -10,
          right: -10,
          child: GestureDetector(
            onTap: () {
              setState(() {
                _proofDocument = null;
              });
            },
            child: Container(
              padding: const EdgeInsets.all(2),
              decoration: const BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.close,
                size: 16,
                color: Colors.white,
              ),
            ),
          ),
        ),
      ],
    );
  }

  // 提交按钮
  Widget _buildSubmitButton() {
    return Container(
      width: double.infinity,
      height: 128.h,
      color: Colors.white,
      // padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 25.w,vertical: 20.h),
        height: 88.h,
        child: ElevatedButton(
          onPressed: _submitApplication,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.primaryColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10.r),
            ),
            elevation: 0,
          ),
          child: const Text(
            '提交',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }



  // 选择证明文件
  Future<void> _pickDocument() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? pickedFile = await picker.pickImage(source: ImageSource.gallery);

      if (pickedFile != null) {
        setState(() {
          _proofDocument = File(pickedFile.path);
        });
      }
    } catch (e) {
      debugPrint('Error picking document: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('选择文件失败，请重试')),
      );
    }
  }

  // 提交申请
  Future<void> _submitApplication() async {
    // 验证输入
    if (_reasonController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入免实习理由')),
      );
      return;
    }

    if (_proofDocument == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请上传证明文件')),
      );
      return;
    }

    try {
      // 第一步：上传文件
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('正在上传文件...')),
      );

      final fileUrl = await _uploadFile();
      if (fileUrl == null) {
        return; // 上传失败，已显示错误信息
      }

      // 第二步：提交免实习申请
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('正在提交申请...')),
      );

      // 获取当前选中的实习计划ID
      final planId = _localStorage.getString('current_plan_id') ?? '8';

      _exemptionApplicationBloc.add(
        SubmitExemptionApplicationEvent(
          planId: int.parse(planId),
          reason: _reasonController.text.trim(),
          fileUrl: fileUrl,
        ),
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('提交失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // 上传文件
  Future<String?> _uploadFile() async {
    if (_proofDocument == null) return null;

    try {
      // 使用简化的文件上传方式
      final uploadUseCase = GetIt.instance<UploadFileUseCase>();

      final result = await uploadUseCase(
        UploadFileParams(
          filePath: _proofDocument!.path,
          fileName: _proofDocument!.path.split('/').last,
          fileType: 'image', // 简化为图片类型
          planId: int.parse(_localStorage.getString('current_plan_id') ?? '8'),
          fileCode: 999, // 免实习申请文件代码
        ),
      );

      return result.fold(
        (failure) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('文件上传失败: ${failure.message}'),
                backgroundColor: Colors.red,
              ),
            );
          }
          return null;
        },
        (uploadResult) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('文件上传成功')),
            );
          }
          return uploadResult.fileUrl;
        },
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('文件上传异常: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return null;
    }
  }
}
