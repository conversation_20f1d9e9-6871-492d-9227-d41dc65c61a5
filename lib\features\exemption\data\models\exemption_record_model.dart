/// -----
/// exemption_record_model.dart
///
/// 免实习记录数据模型
/// 根据API响应结构设计，用于显示单条免实习记录
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_demo/core/widgets/approval_list_item.dart';

/// 免实习记录数据模型
class ExemptionRecordModel implements ApprovalItemData {
  /// 记录ID
  final int recordId;

  /// 学生ID
  final int studentId;

  /// 学生姓名
  final String studentName;

  /// 学生头像URL
  final String avatar;

  /// 实习计划ID
  final int planId;

  /// 免实习原因
  final String reason;

  /// 申请状态 (0=待审批，1=已通过，2=驳回)
  final int statusCode;

  /// 证明材料文件URL
  final String? fileUrl;

  /// 审核人ID
  final int? reviewId;

  /// 审核人姓名
  final String? reviewPerson;

  /// 审核人角色
  final String? reviewRole;

  /// 审核意见
  final String? reviewOpinion;

  /// 创建时间
  final int createTime;

  /// 更新时间
  final int? updateTime;

  /// 创建人
  final String? createPerson;

  /// 更新人
  final String? updatePerson;

  /// 删除标志（0=正常，1=删除）
  final int dr;

  const ExemptionRecordModel({
    required this.recordId,
    required this.studentId,
    required this.studentName,
    required this.avatar,
    required this.planId,
    required this.reason,
    required this.statusCode,
    this.fileUrl,
    this.reviewId,
    this.reviewPerson,
    this.reviewRole,
    this.reviewOpinion,
    required this.createTime,
    this.updateTime,
    this.createPerson,
    this.updatePerson,
    this.dr = 0,
  });

  /// 获取状态文本
  String get statusText {
    switch (statusCode) {
      case 0:
        return '待审批';
      case 1:
        return '已通过';
      case 2:
        return '驳回';
      default:
        return '未知状态';
    }
  }

  /// 获取格式化的提交时间
  String get submitTimeText {
    final dateTime = DateTime.fromMillisecondsSinceEpoch(createTime);
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// 是否为待审批状态
  bool get isPending => statusCode == 0;

  /// 是否为已通过状态
  bool get isApproved => statusCode == 1;

  /// 是否为驳回状态
  bool get isRejected => statusCode == 2;

  // 实现 ApprovalItemData 接口
  @override
  String get id => recordId.toString();

  @override
  String get studentAvatar => avatar;

  @override
  String get status => statusText;

  @override
  String get submitTime => submitTimeText;

  /// 从JSON创建模型实例
  factory ExemptionRecordModel.fromJson(Map<String, dynamic> json) {
    return ExemptionRecordModel(
      recordId: json['id'] ?? 0,
      studentId: json['studentId'] ?? 0,
      studentName: json['studentName'] ?? '',
      avatar: json['avatar'] ?? '',
      planId: json['planId'] ?? 0,
      reason: json['reason'] ?? '',
      statusCode: json['status'] ?? 0,
      fileUrl: json['fileUrl'],
      reviewId: json['reviewId'],
      reviewPerson: json['reviewPerson'],
      reviewRole: json['reviewRole'],
      reviewOpinion: json['reviewOpinion'],
      createTime: json['createTime'] ?? 0,
      updateTime: json['updateTime'],
      createPerson: json['createPerson'],
      updatePerson: json['updatePerson'],
      dr: json['dr'] ?? 0,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': recordId,
      'studentId': studentId,
      'studentName': studentName,
      'avatar': avatar,
      'planId': planId,
      'reason': reason,
      'status': statusCode,
      'fileUrl': fileUrl,
      'reviewId': reviewId,
      'reviewPerson': reviewPerson,
      'reviewRole': reviewRole,
      'reviewOpinion': reviewOpinion,
      'createTime': createTime,
      'updateTime': updateTime,
      'createPerson': createPerson,
      'updatePerson': updatePerson,
      'dr': dr,
    };
  }

  /// 创建副本
  ExemptionRecordModel copyWith({
    int? recordId,
    int? studentId,
    String? studentName,
    String? avatar,
    int? planId,
    String? reason,
    int? statusCode,
    String? fileUrl,
    int? reviewId,
    String? reviewPerson,
    String? reviewRole,
    String? reviewOpinion,
    int? createTime,
    int? updateTime,
    String? createPerson,
    String? updatePerson,
    int? dr,
  }) {
    return ExemptionRecordModel(
      recordId: recordId ?? this.recordId,
      studentId: studentId ?? this.studentId,
      studentName: studentName ?? this.studentName,
      avatar: avatar ?? this.avatar,
      planId: planId ?? this.planId,
      reason: reason ?? this.reason,
      statusCode: statusCode ?? this.statusCode,
      fileUrl: fileUrl ?? this.fileUrl,
      reviewId: reviewId ?? this.reviewId,
      reviewPerson: reviewPerson ?? this.reviewPerson,
      reviewRole: reviewRole ?? this.reviewRole,
      reviewOpinion: reviewOpinion ?? this.reviewOpinion,
      createTime: createTime ?? this.createTime,
      updateTime: updateTime ?? this.updateTime,
      createPerson: createPerson ?? this.createPerson,
      updatePerson: updatePerson ?? this.updatePerson,
      dr: dr ?? this.dr,
    );
  }

  /// 创建示例数据（用于测试和开发）
  static ExemptionRecordModel getSampleData() {
    return ExemptionRecordModel(
      recordId: 1,
      studentId: 110,
      studentName: '王小二',
      avatar: '',
      planId: 8,
      reason: '参军入伍',
      statusCode: 0, // 待审批
      createTime: DateTime.now().subtract(const Duration(days: 2)).millisecondsSinceEpoch,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ExemptionRecordModel && other.recordId == recordId;
  }

  @override
  int get hashCode => recordId.hashCode;

  @override
  String toString() {
    return 'ExemptionRecordModel(id: $recordId, studentName: $studentName, status: $statusText, submitTime: $submitTimeText)';
  }
}
