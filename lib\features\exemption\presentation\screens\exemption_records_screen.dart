/// -----
/// exemption_records_screen.dart
///
/// 学生端免实习记录页面
/// 显示当前学生的免实习申请记录，支持查看申请状态和添加新申请
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

// Flutter 框架库
import 'package:flutter/material.dart';

// 第三方库
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';

// 项目内部库
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/approval_list_item.dart';
import 'package:flutter_demo/core/widgets/course_header_section.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/core/widgets/page_state_widget.dart';
import 'package:flutter_demo/features/exemption/data/models/exemption_record_model.dart';
import 'package:flutter_demo/features/exemption/presentation/bloc/exemption_records_bloc.dart';
import 'package:flutter_demo/features/exemption/presentation/bloc/exemption_records_event.dart';
import 'package:flutter_demo/features/exemption/presentation/bloc/exemption_records_state.dart';
import 'package:flutter_demo/features/plan/presentation/screens/student_exemption_application_screen.dart';

/// 学生端免实习记录页面
///
/// 功能包括：
/// - 显示当前学生的免实习申请记录
/// - 支持查看申请状态（待审核、已通过、已拒绝）
/// - 支持添加新的免实习申请
/// - 支持下拉刷新和上拉加载更多
class ExemptionRecordsScreen extends StatelessWidget {
  const ExemptionRecordsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => GetIt.instance<ExemptionRecordsBloc>()
        ..add(const LoadExemptionRecordsEvent()),
      child: const _ExemptionRecordsView(),
    );
  }
}

class _ExemptionRecordsView extends StatelessWidget {
  const _ExemptionRecordsView();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: const CustomAppBar(
        title: '免实习',
        backgroundColor: Colors.white,
      ),
      body: Column(
        children: [
          // 课程信息头部
          const CourseHeaderSection(),
          
          // 记录列表区域
          Expanded(
            child: BlocBuilder<ExemptionRecordsBloc, ExemptionRecordsState>(
              builder: (context, state) {
                return _buildContent(context, state);
              },
            ),
          ),
        ],
      ),
      // 右下角浮动按钮
      floatingActionButton: _buildFloatingActionButton(context),
    );
  }

  /// 构建主要内容区域
  Widget _buildContent(BuildContext context, ExemptionRecordsState state) {
    if (state is ExemptionRecordsLoadingState) {
      return const PageStateWidget(
        state: PageStateType.initialLoading,
        loadingMessage: '加载免实习记录中...',
      );
    }

    if (state is ExemptionRecordsErrorState) {
      return PageStateWidget(
        state: PageStateType.serverError,
        errorMessage: state.message,
        onRetry: () {
          context.read<ExemptionRecordsBloc>().add(const LoadExemptionRecordsEvent());
        },
      );
    }

    if (state is ExemptionRecordsEmptyState) {
      return PageStateWidget(
        state: PageStateType.empty,
        emptyMessage: state.message,
      );
    }

    if (state is ExemptionRecordsLoadedState) {
      return _buildRecordsList(context, state);
    }

    if (state is ExemptionRecordsRefreshingState) {
      return _buildRecordsListWithRefreshing(context, state.previousRecords);
    }

    return const PageStateWidget(
      state: PageStateType.empty,
    );
  }

  /// 构建记录列表
  Widget _buildRecordsList(BuildContext context, ExemptionRecordsLoadedState state) {
    if (state.records.isEmpty) {
      return const PageStateWidget(
        state: PageStateType.empty,
        emptyMessage: '暂无免实习记录',
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        context.read<ExemptionRecordsBloc>().add(const RefreshExemptionRecordsEvent());
      },
      child: ListView.builder(
        padding: EdgeInsets.symmetric(horizontal: 25.w, vertical: 20.h),
        itemCount: state.records.length,
        itemBuilder: (context, index) {
          final record = state.records[index];
          return ApprovalListItem(
            item: record,
            isPending: record.isPending,
            onTap: () => _onRecordTap(context, record),
            onViewTap: () => _onRecordTap(context, record),
            contentBuilder: (item) => _buildRecordContent(context, record),
            attachmentBuilder: record.fileUrl != null && record.fileUrl!.isNotEmpty
                ? (item) => _buildFileAttachment(context, record.fileUrl!)
                : null,
          );
        },
      ),
    );
  }

  /// 构建带刷新状态的记录列表
  Widget _buildRecordsListWithRefreshing(BuildContext context, List<ExemptionRecordModel> records) {
    if (records.isEmpty) {
      return const PageStateWidget(
        state: PageStateType.empty,
        emptyMessage: '暂无免实习记录',
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        context.read<ExemptionRecordsBloc>().add(const RefreshExemptionRecordsEvent());
      },
      child: ListView.builder(
        padding: EdgeInsets.symmetric(horizontal: 25.w, vertical: 20.h),
        itemCount: records.length,
        itemBuilder: (context, index) {
          final record = records[index];
          return ApprovalListItem(
            item: record,
            isPending: record.isPending,
            onTap: () => _onRecordTap(context, record),
            onViewTap: () => _onRecordTap(context, record),
            contentBuilder: (item) => _buildRecordContent(context, record),
            attachmentBuilder: record.fileUrl != null && record.fileUrl!.isNotEmpty
                ? (item) => _buildFileAttachment(context, record.fileUrl!)
                : null,
          );
        },
      ),
    );
  }

  /// 构建记录内容
  Widget _buildRecordContent(BuildContext context, ExemptionRecordModel record) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        buildInfoRow('学生去向', '参军'),
        // 免实习原因
        buildInfoRow('证明文件', record.reason),

        // 审核信息
        if (record.reviewOpinion != null && record.reviewOpinion!.isNotEmpty) ...[
          SizedBox(height: 8.h),
          buildInfoRow('审核人', record.reviewPerson ?? ''),
          SizedBox(height: 8.h),
          buildInfoRow('审核意见', record.reviewOpinion!),
        ],
      ],
    );
  }

  /// 构建文件附件
  Widget _buildFileAttachment(BuildContext context, String fileUrl) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Row(
        children: [
          Icon(
            Icons.attach_file,
            size: 20.sp,
            color: AppTheme.primaryColor,
          ),
          SizedBox(width: 8.w),
          Expanded(
            child: Text(
              '证明文件',
              style: TextStyle(
                fontSize: 14.sp,
                color: AppTheme.primaryColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Icon(
            Icons.download,
            size: 16.sp,
            color: AppTheme.primaryColor,
          ),
        ],
      ),
    );
  }








  /// 构建右下角浮动按钮
  Widget _buildFloatingActionButton(BuildContext context) {
    return Container(
      width: 100.w,
      height: 100.w,
      margin: EdgeInsets.only(right: 20.w, bottom: 20.h),
      child: FloatingActionButton(
        onPressed: () => _onAddNewApplication(context),
        backgroundColor: const Color(0xFF2165F6),
        elevation: 6,
        shape: const CircleBorder(), // 确保是完美的圆形
        child: Icon(
          Icons.add,
          color: Colors.white,
          size: 40.sp,
        ),
      ),
    );
  }

  /// 处理记录点击事件
  void _onRecordTap(BuildContext context, ExemptionRecordModel record) {
    // TODO(feature): 导航到免实习记录详情页面
    // 这里可以显示详情弹窗或导航到详情页面
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('记录详情'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('申请状态：${record.statusText}'),
            SizedBox(height: 8.h),
            Text('免实习原因：${record.reason}'),
            SizedBox(height: 8.h),
            Text('提交时间：${record.submitTimeText}'),
            if (record.reviewOpinion != null && record.reviewOpinion!.isNotEmpty) ...[
              SizedBox(height: 8.h),
              Text('审核人：${record.reviewPerson ?? ''}'),
              SizedBox(height: 8.h),
              Text('审核意见：${record.reviewOpinion}'),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  /// 处理添加新申请事件
  void _onAddNewApplication(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const StudentExemptionApplicationScreen(),
      ),
    ).then((result) {
      // 只有当提交成功时才刷新列表
      if (context.mounted && result == true) {
        context.read<ExemptionRecordsBloc>().add(const RefreshExemptionRecordsEvent());
      }
    });
  }
}
